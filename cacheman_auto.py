import pyautogui
import time
import cv2
import numpy as np
from PIL import ImageGrab
import win32gui
import win32con
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 禁用pyautogui的安全检查
pyautogui.FAILSAFE = False

class CachemanAutomator:
    def __init__(self):
        self.window_handle = None
        self.window_rect = None
        
    def find_cacheman_window(self):
        """查找Cacheman窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if "Cacheman" in title and "文件资源管理器" not in title and "File Explorer" not in title:
                    windows.append((hwnd, title))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if not windows:
            logger.error("未找到Cacheman窗口，请确保软件已启动")
            return False

        # 选择最大的窗口作为主窗口
        target_handle = None
        max_area = 0

        for handle, title in windows:
            try:
                rect = win32gui.GetWindowRect(handle)
                area = (rect[2] - rect[0]) * (rect[3] - rect[1])
                logger.info(f"找到窗口: '{title}', 面积: {area}")

                if area > max_area:
                    max_area = area
                    target_handle = handle

            except Exception as e:
                logger.warning(f"获取窗口信息失败: {e}")
                continue

        if target_handle:
            self.window_handle = target_handle
            self.window_rect = win32gui.GetWindowRect(target_handle)
            window_title = win32gui.GetWindowText(target_handle)
            logger.info(f"选择目标窗口: {window_title}")

            # 将窗口置于前台
            win32gui.SetForegroundWindow(target_handle)
            time.sleep(0.5)

            return True
        else:
            logger.error("未找到合适的Cacheman窗口")
            return False

    def capture_window(self):
        """截取窗口图像"""
        try:
            if not self.window_handle:
                return None

            # 获取窗口位置
            rect = win32gui.GetWindowRect(self.window_handle)

            # 截取窗口图像
            screenshot = ImageGrab.grab(bbox=rect)
            return np.array(screenshot)

        except Exception as e:
            logger.error(f"截取窗口失败: {e}")
            return None

    def find_button_by_text(self, text_list, screenshot=None):
        """通过OCR查找按钮位置"""
        if screenshot is None:
            screenshot = self.capture_window()

        if screenshot is None:
            return None

        # 这里可以集成OCR库来识别文字
        # 暂时返回None，后面会用图像匹配方法
        return None

    def click_at_position(self, x, y):
        """在指定位置点击"""
        try:
            # 将相对坐标转换为绝对坐标
            abs_x = self.window_rect[0] + x
            abs_y = self.window_rect[1] + y

            # 点击
            pyautogui.click(abs_x, abs_y)
            logger.info(f"点击位置: ({abs_x}, {abs_y})")
            time.sleep(0.5)
            return True

        except Exception as e:
            logger.error(f"点击失败: {e}")
            return False
    
    def step1_click_cancel(self):
        """步骤1: 点击取消按钮 - 使用固定坐标"""
        logger.info("执行步骤1: 点击取消按钮")
        try:
            # 根据你的截图，取消按钮大概在弹窗的右下角
            # 这里使用相对于窗口的坐标（需要根据实际情况调整）
            cancel_x = 680  # 相对于窗口左上角的X坐标
            cancel_y = 495  # 相对于窗口左上角的Y坐标

            return self.click_at_position(cancel_x, cancel_y)

        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            return False

    def step2_click_release_memory(self):
        """步骤2: 点击释放内存按钮"""
        logger.info("执行步骤2: 点击释放内存按钮")
        try:
            # 根据截图，释放内存按钮在工具栏中
            # 需要根据实际位置调整坐标
            release_x = 585  # 工具栏中释放内存按钮的X坐标
            release_y = 50   # 工具栏的Y坐标

            return self.click_at_position(release_x, release_y)

        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            return False

    def step3_click_auto_optimize(self):
        """步骤3: 点击自动优化按钮"""
        logger.info("执行步骤3: 点击自动优化按钮")
        try:
            # 自动优化按钮在工具栏最左边
            auto_optimize_x = 55   # 自动优化按钮的X坐标
            auto_optimize_y = 50   # 工具栏的Y坐标

            return self.click_at_position(auto_optimize_x, auto_optimize_y)

        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            return False

    def step4_select_max_performance(self):
        """步骤4: 选择最大性能选项"""
        logger.info("执行步骤4: 选择最大性能选项")
        try:
            time.sleep(1)  # 等待弹窗出现

            # 最大性能选项的单选按钮位置
            max_perf_x = 350  # 最大性能单选按钮的X坐标
            max_perf_y = 360  # 最大性能单选按钮的Y坐标

            return self.click_at_position(max_perf_x, max_perf_y)

        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            return False

    def step5_click_optimize(self):
        """步骤5: 点击优化按钮"""
        logger.info("执行步骤5: 点击优化按钮")
        try:
            # 优化按钮在弹窗的左下角
            optimize_x = 565  # 优化按钮的X坐标
            optimize_y = 495  # 优化按钮的Y坐标

            return self.click_at_position(optimize_x, optimize_y)

        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            return False
    
    def step1_click_cancel(self):
        """步骤1: 点击取消按钮"""
        logger.info("执行步骤1: 点击取消按钮")
        try:
            # 使用改进的控件查找方法
            cancel_button = self.find_control_with_fallback(
                primary_title="取消",
                fallback_titles=["Cancel", ".*取消.*", ".*Cancel.*"],
                control_type="Button"
            )

            if cancel_button:
                return self.wait_and_click(cancel_button)
            else:
                logger.warning("未找到取消按钮，可能弹窗已关闭")
                return True
        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            return False
    
    def step2_click_release_memory(self):
        """步骤2: 点击释放内存按钮"""
        logger.info("执行步骤2: 点击释放内存按钮")
        try:
            # 使用改进的控件查找方法
            release_button = self.find_control_with_fallback(
                primary_title="释放内存",
                fallback_titles=["Release Memory", ".*释放.*", ".*Release.*", "Free Memory"],
                control_type="Button"
            )

            if release_button:
                return self.wait_and_click(release_button)
            else:
                logger.error("未找到释放内存按钮")
                return False
        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            return False
    
    def step3_click_auto_optimize(self):
        """步骤3: 点击自动优化按钮"""
        logger.info("执行步骤3: 点击自动优化按钮")
        try:
            # 查找自动优化按钮
            auto_optimize_button = self.main_window.child_window(title="自动优化", control_type="Button")
            if not auto_optimize_button.exists():
                # 尝试英文版本
                auto_optimize_button = self.main_window.child_window(title="Auto Optimize", control_type="Button")
            
            if auto_optimize_button.exists():
                return self.wait_and_click(auto_optimize_button)
            else:
                logger.error("未找到自动优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            return False
    
    def step4_select_max_performance(self):
        """步骤4: 选择最大性能选项"""
        logger.info("执行步骤4: 选择最大性能选项")
        try:
            time.sleep(1)  # 等待弹窗出现
            
            # 查找最大性能的单选按钮
            max_performance_radio = self.main_window.child_window(title="最大性能", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试查找包含"最大性能"文本的控件
                max_performance_radio = self.main_window.child_window(title_re=".*最大性能.*", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试英文版本
                max_performance_radio = self.main_window.child_window(title_re=".*Maximum.*Performance.*", control_type="RadioButton")
            
            if max_performance_radio.exists():
                return self.wait_and_click(max_performance_radio)
            else:
                logger.error("未找到最大性能选项")
                return False
        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            return False
    
    def step5_click_optimize(self):
        """步骤5: 点击优化按钮"""
        logger.info("执行步骤5: 点击优化按钮")
        try:
            # 查找优化按钮
            optimize_button = self.main_window.child_window(title="优化", control_type="Button")
            if not optimize_button.exists():
                # 尝试英文版本
                optimize_button = self.main_window.child_window(title="Optimize", control_type="Button")
            
            if optimize_button.exists():
                return self.wait_and_click(optimize_button)
            else:
                logger.error("未找到优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            return False
    
    def execute_automation(self):
        """执行完整的自动化流程"""
        logger.info("开始执行Cacheman自动化操作")
        
        if not self.connect_to_cacheman():
            return False
        
        # 执行5个步骤
        steps = [
            self.step1_click_cancel,
            self.step2_click_release_memory,
            self.step3_click_auto_optimize,
            self.step4_select_max_performance,
            self.step5_click_optimize
        ]
        
        for i, step in enumerate(steps, 1):
            logger.info(f"准备执行步骤{i}")
            if not step():
                logger.error(f"步骤{i}执行失败，停止自动化流程")
                return False
            time.sleep(1)  # 步骤间等待
        
        logger.info("所有步骤执行完成")
        return True

def main():
    """主函数"""
    automator = CachemanAutomator()
    success = automator.execute_automation()
    
    if success:
        print("✅ Cacheman自动化操作完成")
    else:
        print("❌ Cacheman自动化操作失败")

if __name__ == "__main__":
    main()
