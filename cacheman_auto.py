import pyautogui
import time
from pywinauto import Application, findwindows
from pywinauto.controls.uiawrapper import UIAWrapper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CachemanAutomator:
    def __init__(self):
        self.app = None
        self.main_window = None
        
    def connect_to_cacheman(self):
        """连接到Cacheman应用程序"""
        try:
            # 方法1: 通过窗口标题查找
            windows = findwindows.find_windows(title_re=".*Cacheman.*")
            if not windows:
                logger.error("未找到Cacheman窗口，请确保软件已启动")
                return False

            # 如果找到多个窗口，选择主窗口（通常是第一个或最大的）
            target_handle = None
            if len(windows) > 1:
                logger.info(f"找到{len(windows)}个Cacheman窗口，选择主窗口")
                # 选择可见且最大的窗口
                import win32gui
                max_area = 0
                for handle in windows:
                    if win32gui.IsWindowVisible(handle):
                        rect = win32gui.GetWindowRect(handle)
                        area = (rect[2] - rect[0]) * (rect[3] - rect[1])
                        if area > max_area:
                            max_area = area
                            target_handle = handle
            else:
                target_handle = windows[0]

            if not target_handle:
                logger.error("未找到合适的Cacheman主窗口")
                return False

            # 连接到应用程序
            self.app = Application().connect(handle=target_handle)
            self.main_window = self.app.window(handle=target_handle)

            # 验证窗口是否正确
            window_title = self.main_window.window_text()
            logger.info(f"成功连接到窗口: {window_title}")

            # 确保窗口在前台
            self.main_window.set_focus()

            return True

        except Exception as e:
            logger.error(f"连接Cacheman失败: {e}")
            return False

    def verify_window_active(self):
        """验证目标窗口是否仍然活跃"""
        try:
            if self.main_window and self.main_window.exists():
                return True
            else:
                logger.warning("目标窗口已关闭或不存在")
                return False
        except Exception as e:
            logger.error(f"验证窗口状态失败: {e}")
            return False
    
    def wait_and_click(self, control, timeout=5):
        """等待控件可用并点击"""
        try:
            # 确保主窗口仍然活跃
            if not self.verify_window_active():
                return False

            control.wait('ready', timeout=timeout)

            # 确保窗口在前台
            self.main_window.set_focus()
            time.sleep(0.2)

            control.click()
            time.sleep(0.5)  # 短暂等待界面响应
            logger.info(f"成功点击控件: {control}")
            return True
        except Exception as e:
            logger.error(f"点击控件失败: {e}")
            return False

    def find_control_with_fallback(self, primary_title, fallback_titles=None, control_type="Button"):
        """使用多种方式查找控件"""
        if fallback_titles is None:
            fallback_titles = []

        # 尝试主要标题
        try:
            control = self.main_window.child_window(title=primary_title, control_type=control_type)
            if control.exists():
                return control
        except:
            pass

        # 尝试备选标题
        for title in fallback_titles:
            try:
                if ".*" in title:  # 正则表达式
                    control = self.main_window.child_window(title_re=title, control_type=control_type)
                else:
                    control = self.main_window.child_window(title=title, control_type=control_type)
                if control.exists():
                    return control
            except:
                continue

        return None
    
    def step1_click_cancel(self):
        """步骤1: 点击取消按钮"""
        logger.info("执行步骤1: 点击取消按钮")
        try:
            # 查找取消按钮
            cancel_button = self.main_window.child_window(title="取消", control_type="Button")
            if cancel_button.exists():
                return self.wait_and_click(cancel_button)
            else:
                # 尝试英文版本
                cancel_button = self.main_window.child_window(title="Cancel", control_type="Button")
                if cancel_button.exists():
                    return self.wait_and_click(cancel_button)
                else:
                    logger.warning("未找到取消按钮，可能弹窗已关闭")
                    return True
        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            return False
    
    def step2_click_release_memory(self):
        """步骤2: 点击释放内存按钮"""
        logger.info("执行步骤2: 点击释放内存按钮")
        try:
            # 查找释放内存按钮（可能在工具栏中）
            release_button = self.main_window.child_window(title="释放内存", control_type="Button")
            if not release_button.exists():
                # 尝试其他可能的文本
                release_button = self.main_window.child_window(title_re=".*释放.*", control_type="Button")
            if not release_button.exists():
                # 尝试英文版本
                release_button = self.main_window.child_window(title_re=".*Release.*", control_type="Button")
            
            if release_button.exists():
                return self.wait_and_click(release_button)
            else:
                logger.error("未找到释放内存按钮")
                return False
        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            return False
    
    def step3_click_auto_optimize(self):
        """步骤3: 点击自动优化按钮"""
        logger.info("执行步骤3: 点击自动优化按钮")
        try:
            # 查找自动优化按钮
            auto_optimize_button = self.main_window.child_window(title="自动优化", control_type="Button")
            if not auto_optimize_button.exists():
                # 尝试英文版本
                auto_optimize_button = self.main_window.child_window(title="Auto Optimize", control_type="Button")
            
            if auto_optimize_button.exists():
                return self.wait_and_click(auto_optimize_button)
            else:
                logger.error("未找到自动优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            return False
    
    def step4_select_max_performance(self):
        """步骤4: 选择最大性能选项"""
        logger.info("执行步骤4: 选择最大性能选项")
        try:
            time.sleep(1)  # 等待弹窗出现
            
            # 查找最大性能的单选按钮
            max_performance_radio = self.main_window.child_window(title="最大性能", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试查找包含"最大性能"文本的控件
                max_performance_radio = self.main_window.child_window(title_re=".*最大性能.*", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试英文版本
                max_performance_radio = self.main_window.child_window(title_re=".*Maximum.*Performance.*", control_type="RadioButton")
            
            if max_performance_radio.exists():
                return self.wait_and_click(max_performance_radio)
            else:
                logger.error("未找到最大性能选项")
                return False
        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            return False
    
    def step5_click_optimize(self):
        """步骤5: 点击优化按钮"""
        logger.info("执行步骤5: 点击优化按钮")
        try:
            # 查找优化按钮
            optimize_button = self.main_window.child_window(title="优化", control_type="Button")
            if not optimize_button.exists():
                # 尝试英文版本
                optimize_button = self.main_window.child_window(title="Optimize", control_type="Button")
            
            if optimize_button.exists():
                return self.wait_and_click(optimize_button)
            else:
                logger.error("未找到优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            return False
    
    def execute_automation(self):
        """执行完整的自动化流程"""
        logger.info("开始执行Cacheman自动化操作")
        
        if not self.connect_to_cacheman():
            return False
        
        # 执行5个步骤
        steps = [
            self.step1_click_cancel,
            self.step2_click_release_memory,
            self.step3_click_auto_optimize,
            self.step4_select_max_performance,
            self.step5_click_optimize
        ]
        
        for i, step in enumerate(steps, 1):
            logger.info(f"准备执行步骤{i}")
            if not step():
                logger.error(f"步骤{i}执行失败，停止自动化流程")
                return False
            time.sleep(1)  # 步骤间等待
        
        logger.info("所有步骤执行完成")
        return True

def main():
    """主函数"""
    automator = CachemanAutomator()
    success = automator.execute_automation()
    
    if success:
        print("✅ Cacheman自动化操作完成")
    else:
        print("❌ Cacheman自动化操作失败")

if __name__ == "__main__":
    main()
