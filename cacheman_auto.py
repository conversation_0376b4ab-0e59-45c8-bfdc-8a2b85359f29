import pyautogui
import time
from pywinauto import Application, findwindows
from pywinauto.controls.uiawrapper import UIAWrapper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CachemanAutomator:
    def __init__(self):
        self.app = None
        self.main_window = None
        
    def connect_to_cacheman(self):
        """连接到Cacheman应用程序"""
        try:
            # 尝试连接到已运行的Cacheman窗口
            windows = findwindows.find_windows(title_re=".*Cacheman.*")
            if not windows:
                logger.error("未找到Cacheman窗口，请确保软件已启动")
                return False
                
            self.app = Application().connect(handle=windows[0])
            self.main_window = self.app.window(handle=windows[0])
            logger.info("成功连接到Cacheman应用程序")
            return True
            
        except Exception as e:
            logger.error(f"连接Cacheman失败: {e}")
            return False
    
    def wait_and_click(self, control, timeout=5):
        """等待控件可用并点击"""
        try:
            control.wait('ready', timeout=timeout)
            control.click()
            time.sleep(0.5)  # 短暂等待界面响应
            return True
        except Exception as e:
            logger.error(f"点击控件失败: {e}")
            return False
    
    def step1_click_cancel(self):
        """步骤1: 点击取消按钮"""
        logger.info("执行步骤1: 点击取消按钮")
        try:
            # 查找取消按钮
            cancel_button = self.main_window.child_window(title="取消", control_type="Button")
            if cancel_button.exists():
                return self.wait_and_click(cancel_button)
            else:
                # 尝试英文版本
                cancel_button = self.main_window.child_window(title="Cancel", control_type="Button")
                if cancel_button.exists():
                    return self.wait_and_click(cancel_button)
                else:
                    logger.warning("未找到取消按钮，可能弹窗已关闭")
                    return True
        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            return False
    
    def step2_click_release_memory(self):
        """步骤2: 点击释放内存按钮"""
        logger.info("执行步骤2: 点击释放内存按钮")
        try:
            # 查找释放内存按钮（可能在工具栏中）
            release_button = self.main_window.child_window(title="释放内存", control_type="Button")
            if not release_button.exists():
                # 尝试其他可能的文本
                release_button = self.main_window.child_window(title_re=".*释放.*", control_type="Button")
            if not release_button.exists():
                # 尝试英文版本
                release_button = self.main_window.child_window(title_re=".*Release.*", control_type="Button")
            
            if release_button.exists():
                return self.wait_and_click(release_button)
            else:
                logger.error("未找到释放内存按钮")
                return False
        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            return False
    
    def step3_click_auto_optimize(self):
        """步骤3: 点击自动优化按钮"""
        logger.info("执行步骤3: 点击自动优化按钮")
        try:
            # 查找自动优化按钮
            auto_optimize_button = self.main_window.child_window(title="自动优化", control_type="Button")
            if not auto_optimize_button.exists():
                # 尝试英文版本
                auto_optimize_button = self.main_window.child_window(title="Auto Optimize", control_type="Button")
            
            if auto_optimize_button.exists():
                return self.wait_and_click(auto_optimize_button)
            else:
                logger.error("未找到自动优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            return False
    
    def step4_select_max_performance(self):
        """步骤4: 选择最大性能选项"""
        logger.info("执行步骤4: 选择最大性能选项")
        try:
            time.sleep(1)  # 等待弹窗出现
            
            # 查找最大性能的单选按钮
            max_performance_radio = self.main_window.child_window(title="最大性能", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试查找包含"最大性能"文本的控件
                max_performance_radio = self.main_window.child_window(title_re=".*最大性能.*", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试英文版本
                max_performance_radio = self.main_window.child_window(title_re=".*Maximum.*Performance.*", control_type="RadioButton")
            
            if max_performance_radio.exists():
                return self.wait_and_click(max_performance_radio)
            else:
                logger.error("未找到最大性能选项")
                return False
        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            return False
    
    def step5_click_optimize(self):
        """步骤5: 点击优化按钮"""
        logger.info("执行步骤5: 点击优化按钮")
        try:
            # 查找优化按钮
            optimize_button = self.main_window.child_window(title="优化", control_type="Button")
            if not optimize_button.exists():
                # 尝试英文版本
                optimize_button = self.main_window.child_window(title="Optimize", control_type="Button")
            
            if optimize_button.exists():
                return self.wait_and_click(optimize_button)
            else:
                logger.error("未找到优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            return False
    
    def execute_automation(self):
        """执行完整的自动化流程"""
        logger.info("开始执行Cacheman自动化操作")
        
        if not self.connect_to_cacheman():
            return False
        
        # 执行5个步骤
        steps = [
            self.step1_click_cancel,
            self.step2_click_release_memory,
            self.step3_click_auto_optimize,
            self.step4_select_max_performance,
            self.step5_click_optimize
        ]
        
        for i, step in enumerate(steps, 1):
            logger.info(f"准备执行步骤{i}")
            if not step():
                logger.error(f"步骤{i}执行失败，停止自动化流程")
                return False
            time.sleep(1)  # 步骤间等待
        
        logger.info("所有步骤执行完成")
        return True

def main():
    """主函数"""
    automator = CachemanAutomator()
    success = automator.execute_automation()
    
    if success:
        print("✅ Cacheman自动化操作完成")
    else:
        print("❌ Cacheman自动化操作失败")

if __name__ == "__main__":
    main()
