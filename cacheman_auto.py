import pyautogui
import time
from pywinauto import Application, findwindows
from pywinauto.controls.uiawrapper import UIAWrapper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CachemanAutomator:
    def __init__(self):
        self.app = None
        self.main_window = None
        
    def connect_to_cacheman(self):
        """连接到Cacheman应用程序"""
        try:
            # 查找所有Cacheman相关窗口
            windows = findwindows.find_windows(title_re=".*Cacheman.*")
            if not windows:
                logger.error("未找到Cacheman窗口，请确保软件已启动")
                return False

            logger.info(f"找到{len(windows)}个Cacheman相关窗口")

            # 打印所有窗口信息以便调试
            import win32gui
            target_handle = None

            for i, handle in enumerate(windows):
                try:
                    title = win32gui.GetWindowText(handle)
                    class_name = win32gui.GetClassName(handle)
                    is_visible = win32gui.IsWindowVisible(handle)
                    rect = win32gui.GetWindowRect(handle)
                    area = (rect[2] - rect[0]) * (rect[3] - rect[1])

                    logger.info(f"窗口{i+1}: 标题='{title}', 类名='{class_name}', 可见={is_visible}, 面积={area}")

                    # 选择主窗口的条件：
                    # 1. 不包含"文件资源管理器"
                    # 2. 可见
                    # 3. 面积较大
                    if (is_visible and
                        "文件资源管理器" not in title and
                        "File Explorer" not in title and
                        area > 10000):  # 面积阈值
                        if not target_handle or area > win32gui.GetWindowRect(target_handle)[2] * win32gui.GetWindowRect(target_handle)[3]:
                            target_handle = handle

                except Exception as e:
                    logger.warning(f"获取窗口{i+1}信息失败: {e}")
                    continue

            # 如果没找到合适的窗口，使用第一个可见窗口
            if not target_handle:
                for handle in windows:
                    if win32gui.IsWindowVisible(handle):
                        title = win32gui.GetWindowText(handle)
                        if "文件资源管理器" not in title and "File Explorer" not in title:
                            target_handle = handle
                            break

            if not target_handle:
                logger.error("未找到合适的Cacheman主窗口")
                return False

            # 连接到应用程序
            self.app = Application().connect(handle=target_handle)
            self.main_window = self.app.window(handle=target_handle)

            # 验证窗口是否正确
            window_title = self.main_window.window_text()
            logger.info(f"选择的目标窗口: {window_title}")

            # 确保窗口在前台
            self.main_window.set_focus()
            time.sleep(0.5)

            return True

        except Exception as e:
            logger.error(f"连接Cacheman失败: {e}")
            return False

    def verify_window_active(self):
        """验证目标窗口是否仍然活跃"""
        try:
            if self.main_window and self.main_window.exists():
                return True
            else:
                logger.warning("目标窗口已关闭或不存在")
                return False
        except Exception as e:
            logger.error(f"验证窗口状态失败: {e}")
            return False
    
    def wait_and_click(self, control, timeout=5):
        """等待控件可用并点击"""
        try:
            # 确保主窗口仍然活跃
            if not self.verify_window_active():
                return False

            control.wait('ready', timeout=timeout)

            # 确保窗口在前台
            self.main_window.set_focus()
            time.sleep(0.2)

            control.click()
            time.sleep(0.5)  # 短暂等待界面响应
            logger.info(f"成功点击控件: {control}")
            return True
        except Exception as e:
            logger.error(f"点击控件失败: {e}")
            return False

    def find_control_with_fallback(self, primary_title, fallback_titles=None, control_type="Button"):
        """使用多种方式查找控件"""
        if fallback_titles is None:
            fallback_titles = []

        logger.info(f"查找控件: {primary_title}, 类型: {control_type}")

        # 首先打印所有可用的控件以便调试
        try:
            logger.info("当前窗口的所有控件:")
            self.main_window.print_control_identifiers()
        except Exception as e:
            logger.warning(f"无法打印控件列表: {e}")

        # 尝试主要标题
        try:
            control = self.main_window.child_window(title=primary_title, control_type=control_type)
            if control.exists():
                logger.info(f"找到控件: {primary_title}")
                return control
        except Exception as e:
            logger.debug(f"主标题查找失败: {e}")

        # 尝试备选标题
        for title in fallback_titles:
            try:
                if ".*" in title:  # 正则表达式
                    control = self.main_window.child_window(title_re=title, control_type=control_type)
                else:
                    control = self.main_window.child_window(title=title, control_type=control_type)
                if control.exists():
                    logger.info(f"找到控件(备选): {title}")
                    return control
            except Exception as e:
                logger.debug(f"备选标题 '{title}' 查找失败: {e}")
                continue

        # 尝试不指定控件类型
        try:
            control = self.main_window.child_window(title=primary_title)
            if control.exists():
                logger.info(f"找到控件(无类型限制): {primary_title}")
                return control
        except:
            pass

        logger.warning(f"未找到控件: {primary_title}")
        return None
    
    def step1_click_cancel(self):
        """步骤1: 点击取消按钮"""
        logger.info("执行步骤1: 点击取消按钮")
        try:
            # 使用改进的控件查找方法
            cancel_button = self.find_control_with_fallback(
                primary_title="取消",
                fallback_titles=["Cancel", ".*取消.*", ".*Cancel.*"],
                control_type="Button"
            )

            if cancel_button:
                return self.wait_and_click(cancel_button)
            else:
                logger.warning("未找到取消按钮，可能弹窗已关闭")
                return True
        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            return False
    
    def step2_click_release_memory(self):
        """步骤2: 点击释放内存按钮"""
        logger.info("执行步骤2: 点击释放内存按钮")
        try:
            # 使用改进的控件查找方法
            release_button = self.find_control_with_fallback(
                primary_title="释放内存",
                fallback_titles=["Release Memory", ".*释放.*", ".*Release.*", "Free Memory"],
                control_type="Button"
            )

            if release_button:
                return self.wait_and_click(release_button)
            else:
                logger.error("未找到释放内存按钮")
                return False
        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            return False
    
    def step3_click_auto_optimize(self):
        """步骤3: 点击自动优化按钮"""
        logger.info("执行步骤3: 点击自动优化按钮")
        try:
            # 查找自动优化按钮
            auto_optimize_button = self.main_window.child_window(title="自动优化", control_type="Button")
            if not auto_optimize_button.exists():
                # 尝试英文版本
                auto_optimize_button = self.main_window.child_window(title="Auto Optimize", control_type="Button")
            
            if auto_optimize_button.exists():
                return self.wait_and_click(auto_optimize_button)
            else:
                logger.error("未找到自动优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            return False
    
    def step4_select_max_performance(self):
        """步骤4: 选择最大性能选项"""
        logger.info("执行步骤4: 选择最大性能选项")
        try:
            time.sleep(1)  # 等待弹窗出现
            
            # 查找最大性能的单选按钮
            max_performance_radio = self.main_window.child_window(title="最大性能", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试查找包含"最大性能"文本的控件
                max_performance_radio = self.main_window.child_window(title_re=".*最大性能.*", control_type="RadioButton")
            if not max_performance_radio.exists():
                # 尝试英文版本
                max_performance_radio = self.main_window.child_window(title_re=".*Maximum.*Performance.*", control_type="RadioButton")
            
            if max_performance_radio.exists():
                return self.wait_and_click(max_performance_radio)
            else:
                logger.error("未找到最大性能选项")
                return False
        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            return False
    
    def step5_click_optimize(self):
        """步骤5: 点击优化按钮"""
        logger.info("执行步骤5: 点击优化按钮")
        try:
            # 查找优化按钮
            optimize_button = self.main_window.child_window(title="优化", control_type="Button")
            if not optimize_button.exists():
                # 尝试英文版本
                optimize_button = self.main_window.child_window(title="Optimize", control_type="Button")
            
            if optimize_button.exists():
                return self.wait_and_click(optimize_button)
            else:
                logger.error("未找到优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            return False
    
    def execute_automation(self):
        """执行完整的自动化流程"""
        logger.info("开始执行Cacheman自动化操作")
        
        if not self.connect_to_cacheman():
            return False
        
        # 执行5个步骤
        steps = [
            self.step1_click_cancel,
            self.step2_click_release_memory,
            self.step3_click_auto_optimize,
            self.step4_select_max_performance,
            self.step5_click_optimize
        ]
        
        for i, step in enumerate(steps, 1):
            logger.info(f"准备执行步骤{i}")
            if not step():
                logger.error(f"步骤{i}执行失败，停止自动化流程")
                return False
            time.sleep(1)  # 步骤间等待
        
        logger.info("所有步骤执行完成")
        return True

def main():
    """主函数"""
    automator = CachemanAutomator()
    success = automator.execute_automation()
    
    if success:
        print("✅ Cacheman自动化操作完成")
    else:
        print("❌ Cacheman自动化操作失败")

if __name__ == "__main__":
    main()
