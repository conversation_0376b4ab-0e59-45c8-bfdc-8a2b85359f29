import pyautogui
import time
import win32gui
import win32con
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 禁用pyautogui的安全检查
pyautogui.FAILSAFE = False

class CachemanAutomator:
    def __init__(self):
        self.window_handle = None
        self.window_rect = None

    def find_cacheman_window(self):
        """查找Cacheman窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if "Cacheman" in title and "文件资源管理器" not in title and "File Explorer" not in title:
                    windows.append((hwnd, title))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if not windows:
            logger.error("未找到Cacheman窗口，请确保软件已启动")
            return False

        # 选择最大的窗口作为主窗口
        target_handle = None
        max_area = 0

        logger.info(f"找到{len(windows)}个Cacheman相关窗口")

        for i, (handle, title) in enumerate(windows):
            try:
                rect = win32gui.GetWindowRect(handle)
                area = (rect[2] - rect[0]) * (rect[3] - rect[1])
                class_name = win32gui.GetClassName(handle)
                logger.info(f"窗口{i+1}: 标题='{title}', 类名='{class_name}', 面积={area}")

                if area > max_area and area > 10000:  # 面积阈值
                    max_area = area
                    target_handle = handle

            except Exception as e:
                logger.warning(f"获取窗口信息失败: {e}")
                continue

        if target_handle:
            self.window_handle = target_handle
            self.window_rect = win32gui.GetWindowRect(target_handle)
            window_title = win32gui.GetWindowText(target_handle)
            logger.info(f"选择目标窗口: {window_title}")
            logger.info(f"窗口位置: {self.window_rect}")

            # 将窗口置于前台
            win32gui.SetForegroundWindow(target_handle)
            time.sleep(0.5)

            return True
        else:
            logger.error("未找到合适的Cacheman窗口")
            return False

    def click_at_relative_position(self, x_ratio, y_ratio, debug=False):
        """使用相对坐标点击（基于窗口尺寸的比例）"""
        try:
            if not self.window_rect:
                logger.error("窗口坐标未初始化")
                return False

            # 计算窗口尺寸
            window_width = self.window_rect[2] - self.window_rect[0]
            window_height = self.window_rect[3] - self.window_rect[1]

            # 根据比例计算实际坐标
            relative_x = int(window_width * x_ratio)
            relative_y = int(window_height * y_ratio)

            # 转换为绝对坐标
            abs_x = self.window_rect[0] + relative_x
            abs_y = self.window_rect[1] + relative_y

            if debug:
                # 调试模式：显示红色圆圈标记点击位置，但不实际点击
                logger.info(f"调试模式 - 标记位置: ({x_ratio:.3f}, {y_ratio:.3f}) -> 绝对位置: ({abs_x}, {abs_y})")
                logger.info(f"窗口尺寸: {window_width}x{window_height}")
                # 移动鼠标到目标位置但不点击
                pyautogui.moveTo(abs_x, abs_y)
                time.sleep(2)  # 停留2秒让你看到位置
                return True
            else:
                # 正常点击
                pyautogui.click(abs_x, abs_y)
                logger.info(f"点击相对位置: ({x_ratio:.3f}, {y_ratio:.3f}) -> 绝对位置: ({abs_x}, {abs_y})")
                logger.info(f"窗口尺寸: {window_width}x{window_height}")
                time.sleep(0.5)
                return True

        except Exception as e:
            logger.error(f"点击失败: {e}")
            return False

    def debug_all_positions(self):
        """调试模式：显示所有点击位置"""
        logger.info("=== 调试模式：显示所有点击位置 ===")
        positions = [
            (0.67, 0.85, "取消按钮"),
            (0.58, 0.085, "释放内存按钮"),
            (0.055, 0.085, "自动优化按钮"),
            (0.35, 0.62, "最大性能选项"),
            (0.56, 0.85, "优化按钮")
        ]

        for x_ratio, y_ratio, name in positions:
            logger.info(f"显示 {name} 位置...")
            self.click_at_relative_position(x_ratio, y_ratio, debug=True)
            input(f"按回车继续显示下一个位置...")

        logger.info("调试完成")

    def step1_click_cancel(self):
        """步骤1: 点击取消按钮 - 使用相对坐标"""
        logger.info("执行步骤1: 点击取消按钮")
        try:
            # 根据你的截图重新估算：取消按钮在弹窗右下角
            # 调整为更准确的位置
            return self.click_at_relative_position(0.68, 0.82)

        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            return False

    def step2_click_release_memory(self):
        """步骤2: 点击释放内存按钮"""
        logger.info("执行步骤2: 点击释放内存按钮")
        try:
            # 释放内存按钮在工具栏中，重新调整位置
            return self.click_at_relative_position(0.55, 0.08)

        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            return False

    def step3_click_auto_optimize(self):
        """步骤3: 点击自动优化按钮"""
        logger.info("执行步骤3: 点击自动优化按钮")
        try:
            # 自动优化按钮在工具栏最左边，调整位置
            return self.click_at_relative_position(0.04, 0.08)

        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            return False

    def step4_select_max_performance(self):
        """步骤4: 选择最大性能选项"""
        logger.info("执行步骤4: 选择最大性能选项")
        try:
            time.sleep(1)  # 等待弹窗出现

            # 最大性能选项的单选按钮，调整位置
            return self.click_at_relative_position(0.33, 0.58)

        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            return False

    def step5_click_optimize(self):
        """步骤5: 点击优化按钮"""
        logger.info("执行步骤5: 点击优化按钮")
        try:
            # 优化按钮在弹窗左下角，调整位置
            return self.click_at_relative_position(0.54, 0.82)

        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            return False
    
    def execute_automation(self):
        """执行完整的自动化流程"""
        logger.info("开始执行Cacheman自动化操作")

        if not self.find_cacheman_window():
            return False

        # 执行5个步骤
        steps = [
            self.step1_click_cancel,
            self.step2_click_release_memory,
            self.step3_click_auto_optimize,
            self.step4_select_max_performance,
            self.step5_click_optimize
        ]

        for i, step in enumerate(steps, 1):
            logger.info(f"准备执行步骤{i}")
            if not step():
                logger.error(f"步骤{i}执行失败，停止自动化流程")
                return False
            time.sleep(1)  # 步骤间等待

        logger.info("所有步骤执行完成")
        return True

def main():
    """主函数"""
    automator = CachemanAutomator()
    success = automator.execute_automation()
    
    if success:
        print("✅ Cacheman自动化操作完成")
    else:
        print("❌ Cacheman自动化操作失败")

if __name__ == "__main__":
    main()
